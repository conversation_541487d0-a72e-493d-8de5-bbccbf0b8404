package cn.iocoder.yudao.module.urban.dal.dataobject.task;

import cn.iocoder.yudao.module.urban.handler.ArrayTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 社区子区域分割结果 DO
 * 用于存储社区按房屋聚类分割后的子区域信息
 *
 * <AUTHOR>
 */
@Data
public class CommunitySubregionDO {

    /**
     * 子区域ID
     */
    private Integer subregionId;

    /**
     * 子区域几何图形
     */
    private String geom;

    /**
     * 子区域面积
     */
    private BigDecimal area;

    /**
     * 房屋数量
     */
    private Long houseCount;

    /**
     * 房屋ID列表
     */
    @TableField(typeHandler = ArrayTypeHandler.class)
    private List<Long> houseIds;

}
