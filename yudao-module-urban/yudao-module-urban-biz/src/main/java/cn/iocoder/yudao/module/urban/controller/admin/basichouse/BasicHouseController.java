package cn.iocoder.yudao.module.urban.controller.admin.basichouse;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.urban.controller.admin.basichouse.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.basichouse.BasicHouseDO;
import cn.iocoder.yudao.module.urban.service.basichouse.BasicHouseService;

@Tag(name = "管理后台 - 空间数据-住房")
@RestController
@RequestMapping("/urban/basic-house")
@Validated
public class BasicHouseController {

    @Resource
    private BasicHouseService basicHouseService;

    @PostMapping("/create")
    @Operation(summary = "创建空间数据-住房")
    @PreAuthorize("@ss.hasPermission('urban:basic-house:create')")
    public CommonResult<Integer> createBasicHouse(@Valid @RequestBody BasicHouseSaveReqVO createReqVO) {
        return success(basicHouseService.createBasicHouse(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新空间数据-住房")
    @PreAuthorize("@ss.hasPermission('urban:basic-house:update')")
    public CommonResult<Boolean> updateBasicHouse(@Valid @RequestBody BasicHouseSaveReqVO updateReqVO) {
        basicHouseService.updateBasicHouse(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除空间数据-住房")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('urban:basic-house:delete')")
    public CommonResult<Boolean> deleteBasicHouse(@RequestParam("id") Integer id) {
        basicHouseService.deleteBasicHouse(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得空间数据-住房")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('urban:basic-house:query')")
    public CommonResult<BasicHouseRespVO> getBasicHouse(@RequestParam("id") Integer id) {
        BasicHouseDO basicHouse = basicHouseService.getBasicHouse(id);
        return success(BeanUtils.toBean(basicHouse, BasicHouseRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得空间数据-住房分页")
    @PreAuthorize("@ss.hasPermission('urban:basic-house:query')")
    public CommonResult<PageResult<BasicHouseRespVO>> getBasicHousePage(@Valid BasicHousePageReqVO pageReqVO) {
        PageResult<BasicHouseDO> pageResult = basicHouseService.getBasicHousePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BasicHouseRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得空间数据-住房列表")
    @PreAuthorize("@ss.hasPermission('urban:basic-house:query')")
    public CommonResult<List<BasicHouseRespVO>> getBasicHouseList(@Valid BasicHousePageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BasicHouseDO> list = basicHouseService.getBasicHousePage(pageReqVO).getList();
        return success(BeanUtils.toBean(list, BasicHouseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出空间数据-住房 Excel")
    @PreAuthorize("@ss.hasPermission('urban:basic-house:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBasicHouseExcel(@Valid BasicHousePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BasicHouseDO> list = basicHouseService.getBasicHousePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "空间数据-住房.xls", "数据", BasicHouseRespVO.class,
                        BeanUtils.toBean(list, BasicHouseRespVO.class));
    }

}