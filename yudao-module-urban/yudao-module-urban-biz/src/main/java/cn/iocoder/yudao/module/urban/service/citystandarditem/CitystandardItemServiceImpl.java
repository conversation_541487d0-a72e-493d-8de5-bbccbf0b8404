package cn.iocoder.yudao.module.urban.service.citystandarditem;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.urban.controller.admin.citystandarditem.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.citystandarditem.CitystandardItemDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.urban.dal.mysql.citystandarditem.CitystandardItemMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CitystandardItemServiceImpl implements CitystandardItemService {

    @Resource
    private CitystandardItemMapper citystandardItemMapper;

    @Override
    public Long createCitystandardItem(CitystandardItemSaveReqVO createReqVO) {
        // 插入
        CitystandardItemDO citystandardItem = BeanUtils.toBean(createReqVO, CitystandardItemDO.class);
        citystandardItemMapper.insert(citystandardItem);
        // 返回
        return citystandardItem.getId();
    }

    @Override
    public void updateCitystandardItem(CitystandardItemSaveReqVO updateReqVO) {
        // 校验存在
        validateCitystandardItemExists(updateReqVO.getId());
        // 更新
        CitystandardItemDO updateObj = BeanUtils.toBean(updateReqVO, CitystandardItemDO.class);
        citystandardItemMapper.updateById(updateObj);
    }

    @Override
    public void deleteCitystandardItem(Long id) {
        // 校验存在
        validateCitystandardItemExists(id);
        // 删除
        citystandardItemMapper.deleteById(id);
    }

    private void validateCitystandardItemExists(Long id) {
        if (citystandardItemMapper.selectById(id) == null) {
            throw exception(CITYSTANDARD_ITEM_NOT_EXISTS);
        }
    }

    @Override
    public CitystandardItemDO getCitystandardItem(Long id) {
        return citystandardItemMapper.selectById(id);
    }

    @Override
    public PageResult<CitystandardItemDO> getCitystandardItemPage(CitystandardItemPageReqVO pageReqVO) {
        return citystandardItemMapper.selectPage(pageReqVO);
    }

}