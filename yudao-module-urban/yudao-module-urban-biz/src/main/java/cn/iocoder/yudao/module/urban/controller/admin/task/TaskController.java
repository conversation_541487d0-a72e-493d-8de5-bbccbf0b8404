package cn.iocoder.yudao.module.urban.controller.admin.task;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.urban.controller.admin.task.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.module.urban.service.task.TaskService;

@Tag(name = "管理后台 - 任务管理")
@RestController
@RequestMapping("/urban/task")
@Validated
public class TaskController {

    @Resource
    private TaskService taskService;

    @PostMapping("/create")
    @Operation(summary = "创建任务管理")
    @PreAuthorize("@ss.hasPermission('urban:task:create')")
    public CommonResult<Long> createTask(@Valid @RequestBody TaskSaveReqVO createReqVO) {
        return success(taskService.createTask(createReqVO));
    }

    @PostMapping("/batchcreate")
    @Operation(summary = "批量创建任务管理", description = "根据城市标准体系ID、目标类型、分割策略批量创建任务")
    @PreAuthorize("@ss.hasPermission('urban:task:create')")
    @ApiAccessLog(operateType = CREATE)
    public CommonResult<List<Long>> batchCreateTasks(@Valid @RequestBody TaskBatchCreateReqVO batchCreateReqVO) {
        List<Long> taskIds = taskService.batchCreateTasks(batchCreateReqVO);
        return success(taskIds);
    }

    @PutMapping("/update")
    @Operation(summary = "更新任务管理")
    @PreAuthorize("@ss.hasPermission('urban:task:update')")
    public CommonResult<Boolean> updateTask(@Valid @RequestBody TaskSaveReqVO updateReqVO) {
        taskService.updateTask(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除任务管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('urban:task:delete')")
    public CommonResult<Boolean> deleteTask(@RequestParam("id") Long id) {
        taskService.deleteTask(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得任务管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('urban:task:query')")
    public CommonResult<TaskRespVO> getTask(@RequestParam("id") Long id) {
        TaskDO task = taskService.getTask(id);
        return success(BeanUtils.toBean(task, TaskRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得任务管理分页")
    @PreAuthorize("@ss.hasPermission('urban:task:query')")
    public CommonResult<PageResult<TaskRespVO>> getTaskPage(@Valid TaskPageReqVO pageReqVO) {
        PageResult<TaskDO> pageResult = taskService.getTaskPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TaskRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得任务管理全部")
    @PreAuthorize("@ss.hasPermission('urban:task:query')")
    public CommonResult<List<TaskRespVO>> getTaskList(@Valid TaskPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TaskDO> list = taskService.getTaskPage(pageReqVO).getList();
        return success(BeanUtils.toBean(list, TaskRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出任务管理 Excel")
    @PreAuthorize("@ss.hasPermission('urban:task:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTaskExcel(@Valid TaskPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TaskDO> list = taskService.getTaskPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "任务管理.xls", "数据", TaskRespVO.class,
                        BeanUtils.toBean(list, TaskRespVO.class));
    }

}