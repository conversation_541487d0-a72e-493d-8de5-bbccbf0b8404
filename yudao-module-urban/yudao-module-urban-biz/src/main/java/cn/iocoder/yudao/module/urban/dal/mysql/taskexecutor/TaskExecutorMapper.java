package cn.iocoder.yudao.module.urban.dal.mysql.taskexecutor;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor.TaskExecutorDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo.*;

/**
 * 任务执行人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskExecutorMapper extends BaseMapperX<TaskExecutorDO> {

    default PageResult<TaskExecutorDO> selectPage(TaskExecutorPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TaskExecutorDO>()
                .eqIfPresent(TaskExecutorDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(TaskExecutorDO::getUserId, reqVO.getUserId())
                .eqIfPresent(TaskExecutorDO::getUserType, reqVO.getUserType())
                .betweenIfPresent(TaskExecutorDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TaskExecutorDO::getId));
    }

}