package cn.iocoder.yudao.module.urban.service.basichouse;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.urban.controller.admin.basichouse.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.basichouse.BasicHouseDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 空间数据-住房 Service 接口
 *
 * <AUTHOR>
 */
public interface BasicHouseService {

    /**
     * 创建空间数据-住房
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createBasicHouse(@Valid BasicHouseSaveReqVO createReqVO);

    /**
     * 更新空间数据-住房
     *
     * @param updateReqVO 更新信息
     */
    void updateBasicHouse(@Valid BasicHouseSaveReqVO updateReqVO);

    /**
     * 删除空间数据-住房
     *
     * @param id 编号
     */
    void deleteBasicHouse(Integer id);

    /**
     * 获得空间数据-住房
     *
     * @param id 编号
     * @return 空间数据-住房
     */
    BasicHouseDO getBasicHouse(Integer id);

    /**
     * 获得空间数据-住房分页
     *
     * @param pageReqVO 分页查询
     * @return 空间数据-住房分页
     */
    PageResult<BasicHouseDO> getBasicHousePage(BasicHousePageReqVO pageReqVO);

}