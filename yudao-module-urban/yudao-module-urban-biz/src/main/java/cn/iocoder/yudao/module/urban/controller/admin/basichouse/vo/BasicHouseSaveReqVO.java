package cn.iocoder.yudao.module.urban.controller.admin.basichouse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 空间数据-住房新增/修改 Request VO")
@Data
public class BasicHouseSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27863")
    private Integer id;

    @Schema(description = "空间数据")
    private String geom;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "状态", example = "2")
    private Integer status;

}