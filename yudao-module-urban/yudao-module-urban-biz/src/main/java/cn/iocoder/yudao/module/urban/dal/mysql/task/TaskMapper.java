package cn.iocoder.yudao.module.urban.dal.mysql.task;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.CommunitySubregionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.iocoder.yudao.module.urban.controller.admin.task.vo.*;

/**
 * 任务管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskMapper extends BaseMapperX<TaskDO> {

    default PageResult<TaskDO> selectPage(TaskPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TaskDO>()
                .eqIfPresent(TaskDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(TaskDO::getTaskType, reqVO.getTaskType())
                .likeIfPresent(TaskDO::getTaskName, reqVO.getTaskName())
                .eqIfPresent(TaskDO::getProvince, reqVO.getProvince())
                .eqIfPresent(TaskDO::getCity, reqVO.getCity())
                .eqIfPresent(TaskDO::getXzqdm, reqVO.getXzqdm())
                .eqIfPresent(TaskDO::getTown, reqVO.getTown())
                .eqIfPresent(TaskDO::getVillage, reqVO.getVillage())
                .eqIfPresent(TaskDO::getCommunity, reqVO.getCommunity())
                .likeIfPresent(TaskDO::getLeaderName, reqVO.getLeaderName())
                .eqIfPresent(TaskDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TaskDO::getPlannedTime, reqVO.getPlannedTime())
                .betweenIfPresent(TaskDO::getActualDate, reqVO.getActualDate())
                .betweenIfPresent(TaskDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(TaskDO::getGeom, reqVO.getGeom())
                .orderByDesc(TaskDO::getId));
    }

    /**
     * 根据社区ID和分割数量，对社区进行基于房屋聚类的分割
     *
     * @param regionId 社区ID
     * @param splitCount 分割数量
     * @return 分割后的子区域列表
     */
    List<CommunitySubregionDO> selectCommunitySubregionsBySplit(@Param("regionId") String regionId,
                                                               @Param("splitCount") Integer splitCount);

}