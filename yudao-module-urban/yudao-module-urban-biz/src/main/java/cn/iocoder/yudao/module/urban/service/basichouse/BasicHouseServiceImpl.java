package cn.iocoder.yudao.module.urban.service.basichouse;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.urban.controller.admin.basichouse.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.basichouse.BasicHouseDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.urban.dal.mysql.basichouse.BasicHouseMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 空间数据-住房 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BasicHouseServiceImpl implements BasicHouseService {

    @Resource
    private BasicHouseMapper basicHouseMapper;

    @Override
    public Integer createBasicHouse(BasicHouseSaveReqVO createReqVO) {
        // 插入
        BasicHouseDO basicHouse = BeanUtils.toBean(createReqVO, BasicHouseDO.class);
        basicHouseMapper.insert(basicHouse);
        // 返回
        return basicHouse.getId();
    }

    @Override
    public void updateBasicHouse(BasicHouseSaveReqVO updateReqVO) {
        // 校验存在
        validateBasicHouseExists(updateReqVO.getId());
        // 更新
        BasicHouseDO updateObj = BeanUtils.toBean(updateReqVO, BasicHouseDO.class);
        basicHouseMapper.updateById(updateObj);
    }

    @Override
    public void deleteBasicHouse(Integer id) {
        // 校验存在
        validateBasicHouseExists(id);
        // 删除
        basicHouseMapper.deleteById(id);
    }

    private void validateBasicHouseExists(Integer id) {
        if (basicHouseMapper.selectById(id) == null) {
            throw exception(BASIC_HOUSE_NOT_EXISTS);
        }
    }

    @Override
    public BasicHouseDO getBasicHouse(Integer id) {
        return basicHouseMapper.selectById(id);
    }

    @Override
    public PageResult<BasicHouseDO> getBasicHousePage(BasicHousePageReqVO pageReqVO) {
        return basicHouseMapper.selectPage(pageReqVO);
    }

}