package cn.iocoder.yudao.module.urban.dal.dataobject.task;

import cn.iocoder.yudao.module.urban.handler.PgGeometryTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 任务管理 DO
 *
 * <AUTHOR>
 */
@TableName(value = "uc_task",autoResultMap = true)
@KeySequence("uc_task_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 任务编号
     */
    private String taskId;
    /**
     * 城市体系指标ID
     */
    private Long citystandardId;
    /**
     * 任务类型
     *
     * 枚举 {@link TODO uc_task_type 对应的类}
     */
    private String taskType;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 县区
     */
    private String xzqdm;
    /**
     * 镇街
     */
    private String town;
    /**
     * 社区村
     */
    private String village;
    /**
     * 小区
     */
    private String community;
    /**
     * 组长ID
     */
    private String leaderId;
    /**
     * 组长
     */
    private String leaderName;
    /**
     * 状态：0-未完成|1-已完成
     */
    private Integer status;
    /**
     * 计划完成时间
     */
    private LocalDateTime plannedTime;
    /**
     * 实际完成时间
     */
    private LocalDateTime actualDate;
    /**
     * 图形
     */
    @TableField(typeHandler = PgGeometryTypeHandler.class)
    private String geom;
    /**
     * 调查人 ID
     */
    @TableField(exist = false)
    private List<Long> surveyorIds = new ArrayList<>();

}