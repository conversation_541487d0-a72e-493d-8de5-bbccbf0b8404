package cn.iocoder.yudao.module.urban.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 任务管理新增/修改 Request VO")
@Data
public class TaskSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27365")
    private Long id;

    @Schema(description = "任务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21031")
    @NotEmpty(message = "任务编号不能为空")
    private String taskId;

    @Schema(description="城市标准体系ID",requiredMode = Schema.RequiredMode.REQUIRED)
    private Long citystandardId;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "任务类型不能为空")
    private String taskType;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "省不能为空")
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "市不能为空")
    private String city;

    @Schema(description = "县区", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "县区不能为空")
    private String xzqdm;

    @Schema(description = "镇街")
    private String town;

    @Schema(description = "社区村")
    private String village;

    @Schema(description = "小区")
    private String community;

    @Schema(description = "组长ID", example = "27387")
    private String leaderId;

    @Schema(description = "组长", example = "李四")
    private String leaderName;

    @Schema(description = "状态：0-未完成|1-已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "状态：0-未完成|1-已完成不能为空")
    private Short status;

    @Schema(description = "计划完成时间")
    private LocalDateTime plannedTime;

    @Schema(description = "实际完成时间")
    private LocalDateTime actualDate;

    @Schema(description = "图形", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "图形不能为空")
    private String geom;
    @Schema(description = "调查人集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> surveyorIds = new ArrayList<>();

}