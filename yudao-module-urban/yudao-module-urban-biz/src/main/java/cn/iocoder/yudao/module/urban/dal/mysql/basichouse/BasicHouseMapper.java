package cn.iocoder.yudao.module.urban.dal.mysql.basichouse;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.urban.dal.dataobject.basichouse.BasicHouseDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.urban.controller.admin.basichouse.vo.*;

/**
 * 空间数据-住房 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BasicHouseMapper extends BaseMapperX<BasicHouseDO> {

    default PageResult<BasicHouseDO> selectPage(BasicHousePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BasicHouseDO>()
                .eqIfPresent(BasicHouseDO::getGeom, reqVO.getGeom())
                .eqIfPresent(BasicHouseDO::getHeight, reqVO.getHeight())
                .eqIfPresent(BasicHouseDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(BasicHouseDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(BasicHouseDO::getId));
    }

}