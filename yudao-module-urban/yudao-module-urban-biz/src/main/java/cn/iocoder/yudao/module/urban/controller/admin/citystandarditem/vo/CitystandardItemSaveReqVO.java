package cn.iocoder.yudao.module.urban.controller.admin.citystandarditem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性新增/修改 Request VO")
@Data
public class CitystandardItemSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4589")
    private Long id;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "序号不能为空")
    private Long seqNo;

    @Schema(description = "城市体系ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long citystandardId;

    @Schema(description = "是否核心指标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否核心指标不能为空")
    private Integer isCore;

    @Schema(description = "是否国家基础指标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否国家基础指标不能为空")
    private Integer isNational;

    @Schema(description = "是否地方特色指标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否地方特色指标不能为空")
    private Integer isLocal;

    @Schema(description = "是否小程序采集", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否小程序采集不能为空")
    private Integer isApplet;

    @Schema(description = "一级维度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "一级维度不能为空")
    private String dimensionLevel1;

    @Schema(description = "二级维度")
    private String dimensionLevel2;

    @Schema(description = "指标项", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "指标项不能为空")
    private String itemName;

    @Schema(description = "指标项（别名）")
    private String itemAlias;

    @Schema(description = "指标编号")
    private String itemCode;

    @Schema(description = "解释")
    private String explanation;

    @Schema(description = "评价标准")
    private String evaluationCriteria;

    @Schema(description = "数据分解")
    private String dataDecomposition;

    @Schema(description = "数据格式")
    private String dataFormat;

    @Schema(description = "采集内容")
    private String collectionContent;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "责任部门")
    private String responsibleDept;

    @Schema(description = "案例示意")
    private String caseDemo;

}