package cn.iocoder.yudao.module.urban.dal.dataobject.basichouse;

import cn.iocoder.yudao.module.urban.handler.PgGeometryTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 空间数据-住房 DO
 *
 * <AUTHOR>
 */
@TableName(value="uc_basic_house",autoResultMap = true)
@KeySequence("uc_basic_house_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicHouseDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Integer id;
    /**
     * 空间数据
     */
    @TableField(typeHandler = PgGeometryTypeHandler.class)
    private String geom;
    /**
     * 高度
     */
    private Double height;
    /**
     * 状态
     *
     * 枚举 {@link TODO uc_task_status 对应的类}
     */
    private Integer status;

}