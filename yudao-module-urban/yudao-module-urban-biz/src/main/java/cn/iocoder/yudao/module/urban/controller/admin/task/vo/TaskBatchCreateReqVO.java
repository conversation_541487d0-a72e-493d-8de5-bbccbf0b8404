package cn.iocoder.yudao.module.urban.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Schema(description = "管理后台 - 任务管理批量创建 Request VO")
@Data
public class TaskBatchCreateReqVO {

    @Schema(description = "城市标准体系ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "城市标准体系ID不能为空")
    private Long citystandardId;

    @Schema(description = "目标类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "社区")
    @NotEmpty(message = "目标类型不能为空")
    private String targetType;

    @Schema(description = "是否手动分割", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否手动分割不能为空")
    private Boolean manualSplit;

    @Schema(description = "分割数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "4")
    @NotNull(message = "分割数量不能为空")
    @Min(value = 1, message = "分割数量必须大于0")
    private Integer splitCount;

}
